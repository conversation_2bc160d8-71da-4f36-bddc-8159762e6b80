"""Pipeline execution context for sharing data between stages and strategies."""

from typing import Any, Dict, List, Optional
from datetime import datetime

from crawl4ai_mcp.models import SearchResult


class PipelineContext:
    """Context object passed through pipeline execution."""
    
    def __init__(
        self,
        query: str,
        initial_documents: Optional[List[SearchResult]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize pipeline context.
        
        Args:
            query: The search query
            initial_documents: Initial documents (if any)
            metadata: Initial metadata
        """
        self.query = query
        self.original_query = query  # Preserve original even if query is modified
        self.current_documents = initial_documents or []
        self.metadata = metadata or {}
        
        # Execution tracking
        self.start_time = datetime.now()
        self.stage_history: List[str] = []
        self.strategy_metrics: Dict[str, Dict[str, Any]] = {}
        
        # Intermediate results storage
        self.intermediate_results: Dict[str, Any] = {}
        
        # Query modifications/expansions
        self.expanded_queries: List[str] = []
        self.query_embeddings: Dict[str, List[float]] = {}
        
        # Error tracking
        self.errors: List[Dict[str, Any]] = []
    
    def update_documents(self, documents: List[SearchResult]) -> None:
        """
        Update the current document set.
        
        Args:
            documents: New document set
        """
        self.current_documents = documents
    
    def add_stage_history(self, stage_name: str) -> None:
        """
        Record that a stage was executed.
        
        Args:
            stage_name: Name of the executed stage
        """
        self.stage_history.append(stage_name)
    
    def add_strategy_metric(
        self, 
        strategy_type: str, 
        metric_name: str, 
        value: Any
    ) -> None:
        """
        Add a metric for a strategy.
        
        Args:
            strategy_type: Type of strategy
            metric_name: Name of the metric
            value: Metric value
        """
        if strategy_type not in self.strategy_metrics:
            self.strategy_metrics[strategy_type] = {}
        self.strategy_metrics[strategy_type][metric_name] = value
    
    def store_intermediate(self, key: str, value: Any) -> None:
        """
        Store intermediate results that might be needed by later stages.
        
        Args:
            key: Storage key
            value: Value to store
        """
        self.intermediate_results[key] = value
    
    def get_intermediate(self, key: str, default: Any = None) -> Any:
        """
        Retrieve intermediate results.
        
        Args:
            key: Storage key
            default: Default value if key not found
            
        Returns:
            Stored value or default
        """
        return self.intermediate_results.get(key, default)
    
    def add_expanded_query(self, query: str) -> None:
        """
        Add an expanded/modified query.
        
        Args:
            query: Expanded query
        """
        if query not in self.expanded_queries:
            self.expanded_queries.append(query)
    
    def store_query_embedding(self, query: str, embedding: List[float]) -> None:
        """
        Store a query embedding for reuse.
        
        Args:
            query: Query text
            embedding: Query embedding
        """
        self.query_embeddings[query] = embedding
    
    def get_query_embedding(self, query: str) -> Optional[List[float]]:
        """
        Get a stored query embedding.
        
        Args:
            query: Query text
            
        Returns:
            Embedding or None
        """
        return self.query_embeddings.get(query)
    
    def add_error(
        self, 
        component: str, 
        error: str, 
        severity: str = "error"
    ) -> None:
        """
        Record an error.
        
        Args:
            component: Component that generated the error
            error: Error message
            severity: Error severity (error, warning, info)
        """
        self.errors.append({
            "component": component,
            "error": error,
            "severity": severity,
            "timestamp": datetime.now()
        })
    
    def get_execution_time_ms(self) -> float:
        """
        Get total execution time in milliseconds.
        
        Returns:
            Execution time in milliseconds
        """
        delta = datetime.now() - self.start_time
        return delta.total_seconds() * 1000
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert context to dictionary for serialization.
        
        Returns:
            Dictionary representation
        """
        return {
            "query": self.query,
            "original_query": self.original_query,
            "document_count": len(self.current_documents),
            "metadata": self.metadata,
            "execution_time_ms": self.get_execution_time_ms(),
            "stage_history": self.stage_history,
            "strategy_metrics": self.strategy_metrics,
            "expanded_queries": self.expanded_queries,
            "error_count": len(self.errors),
            "errors": self.errors
        }