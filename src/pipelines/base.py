"""Base classes for the RAG pipeline system."""

from enum import Enum
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field

from crawl4ai_mcp.models import SearchResult


class ExecutionMode(str, Enum):
    """Execution mode for pipeline stages."""
    SEQUENCE = "sequence"
    PARALLEL = "parallel"


class StrategyConfig(BaseModel):
    """Configuration for a single strategy within a stage."""
    type: str = Field(description="Strategy type identifier")
    weight: float = Field(default=1.0, ge=0.0, le=1.0, description="Weight for parallel execution")
    config: Dict[str, Any] = Field(default_factory=dict, description="Strategy-specific configuration")
    enabled: bool = Field(default=True, description="Whether this strategy is enabled")


class StageConfig(BaseModel):
    """Configuration for a pipeline stage."""
    name: str = Field(description="Stage name")
    mode: ExecutionMode = Field(default=ExecutionMode.SEQUENCE, description="Execution mode")
    strategies: List[StrategyConfig] = Field(description="List of strategies in this stage")
    enabled: bool = Field(default=True, description="Whether this stage is enabled")
    continue_on_error: bool = Field(default=False, description="Continue pipeline if stage fails")


class PipelineConfig(BaseModel):
    """Configuration for an entire pipeline."""
    name: str = Field(description="Pipeline name")
    description: Optional[str] = Field(default=None, description="Pipeline description")
    stages: List[StageConfig] = Field(description="List of stages in execution order")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    version: str = Field(default="1.0.0", description="Pipeline version")


class StrategyResult(BaseModel):
    """Result from a single strategy execution."""
    strategy_type: str
    documents: List[SearchResult]
    metadata: Dict[str, Any] = Field(default_factory=dict)
    error: Optional[str] = None
    execution_time_ms: Optional[float] = None


class StageResult(BaseModel):
    """Result from a pipeline stage execution."""
    stage_name: str
    mode: ExecutionMode
    strategy_results: List[StrategyResult]
    merged_documents: List[SearchResult]
    metadata: Dict[str, Any] = Field(default_factory=dict)
    error: Optional[str] = None
    execution_time_ms: Optional[float] = None


class PipelineResult(BaseModel):
    """Result from entire pipeline execution."""
    pipeline_name: str
    query: str
    stage_results: List[StageResult]
    final_documents: List[SearchResult]
    metadata: Dict[str, Any] = Field(default_factory=dict)
    total_execution_time_ms: float
    success: bool
    error: Optional[str] = None


class PipelineStage:
    """A stage in the RAG pipeline containing one or more strategies."""
    
    def __init__(self, config: StageConfig):
        """
        Initialize a pipeline stage.
        
        Args:
            config: Stage configuration
        """
        self.config = config
        self.strategies: List[Any] = []  # Will be populated with strategy instances
    
    def add_strategy(self, strategy: Any) -> None:
        """
        Add a strategy to this stage.
        
        Args:
            strategy: Strategy instance
        """
        self.strategies.append(strategy)
    
    def is_parallel(self) -> bool:
        """Check if this stage runs strategies in parallel."""
        return self.config.mode == ExecutionMode.PARALLEL
    
    def is_enabled(self) -> bool:
        """Check if this stage is enabled."""
        return self.config.enabled


class Pipeline:
    """A complete RAG pipeline with multiple stages."""
    
    def __init__(self, config: PipelineConfig):
        """
        Initialize a pipeline.
        
        Args:
            config: Pipeline configuration
        """
        self.config = config
        self.stages: List[PipelineStage] = []
        self._build_stages()
    
    def _build_stages(self) -> None:
        """Build stage instances from configuration."""
        for stage_config in self.config.stages:
            if stage_config.enabled:
                stage = PipelineStage(stage_config)
                self.stages.append(stage)
    
    def get_stage(self, name: str) -> Optional[PipelineStage]:
        """
        Get a stage by name.
        
        Args:
            name: Stage name
            
        Returns:
            PipelineStage instance or None
        """
        for stage in self.stages:
            if stage.config.name == name:
                return stage
        return None
    
    def validate(self) -> List[str]:
        """
        Validate pipeline configuration.
        
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check for empty pipeline
        if not self.stages:
            errors.append("Pipeline has no enabled stages")
        
        # Check for duplicate stage names
        stage_names = [stage.config.name for stage in self.stages]
        if len(stage_names) != len(set(stage_names)):
            errors.append("Duplicate stage names found")
        
        # Check each stage has strategies
        for stage in self.stages:
            if not stage.config.strategies:
                errors.append(f"Stage '{stage.config.name}' has no strategies")
            
            # Check parallel stages have weights that sum to 1.0
            if stage.is_parallel():
                total_weight = sum(
                    s.weight for s in stage.config.strategies if s.enabled
                )
                if abs(total_weight - 1.0) > 0.001:
                    errors.append(
                        f"Stage '{stage.config.name}' weights sum to {total_weight}, not 1.0"
                    )
        
        return errors