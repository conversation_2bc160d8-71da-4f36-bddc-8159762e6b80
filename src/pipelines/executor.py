"""Pipeline execution engine."""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from crawl4ai_mcp.models import SearchResult
from crawl4ai_mcp.pipelines.base import (
    Pipeline,
    PipelineResult,
    StageResult,
    StrategyResult,
    ExecutionMode,
)
from crawl4ai_mcp.pipelines.context import PipelineContext
from crawl4ai_mcp.pipelines.mergers import ResultMerger
from crawl4ai_mcp.strategies.registry import strategy_registry

logger = logging.getLogger(__name__)


class PipelineExecutor:
    """Executes RAG pipelines."""
    
    def __init__(self, result_merger: Optional[ResultMerger] = None):
        """
        Initialize pipeline executor.
        
        Args:
            result_merger: Result merger instance (optional)
        """
        self.result_merger = result_merger or ResultMerger()
    
    async def execute(
        self,
        pipeline: Pipeline,
        query: str,
        initial_documents: Optional[List[SearchResult]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> PipelineResult:
        """
        Execute a pipeline.
        
        Args:
            pipeline: Pipeline to execute
            query: Search query
            initial_documents: Initial documents (optional)
            metadata: Additional metadata
            
        Returns:
            Pipeline execution result
        """
        start_time = datetime.now()
        context = PipelineContext(query, initial_documents, metadata)
        stage_results: List[StageResult] = []
        
        try:
            # Validate pipeline before execution
            errors = pipeline.validate()
            if errors:
                return self._create_error_result(
                    pipeline.config.name,
                    query,
                    f"Pipeline validation failed: {'; '.join(errors)}",
                    start_time
                )
            
            # Execute each stage
            for stage in pipeline.stages:
                if not stage.is_enabled():
                    logger.info(f"Skipping disabled stage: {stage.config.name}")
                    continue
                
                logger.info(f"Executing stage: {stage.config.name}")
                context.add_stage_history(stage.config.name)
                
                try:
                    stage_result = await self._execute_stage(stage, context)
                    stage_results.append(stage_result)
                    
                    # Update context with stage results
                    context.update_documents(stage_result.merged_documents)
                    
                except Exception as e:
                    error_msg = f"Stage '{stage.config.name}' failed: {str(e)}"
                    logger.error(error_msg)
                    context.add_error(stage.config.name, error_msg)
                    
                    if not stage.config.continue_on_error:
                        return self._create_error_result(
                            pipeline.config.name,
                            query,
                            error_msg,
                            start_time,
                            stage_results
                        )
                    
                    # Add error stage result and continue
                    stage_results.append(
                        StageResult(
                            stage_name=stage.config.name,
                            mode=stage.config.mode,
                            strategy_results=[],
                            merged_documents=[],
                            error=error_msg
                        )
                    )
            
            # Create successful result
            return PipelineResult(
                pipeline_name=pipeline.config.name,
                query=query,
                stage_results=stage_results,
                final_documents=context.current_documents,
                metadata=context.to_dict(),
                total_execution_time_ms=context.get_execution_time_ms(),
                success=True
            )
            
        except Exception as e:
            error_msg = f"Pipeline execution failed: {str(e)}"
            logger.error(error_msg)
            return self._create_error_result(
                pipeline.config.name,
                query,
                error_msg,
                start_time,
                stage_results
            )
    
    async def _execute_stage(
        self,
        stage: Any,  # PipelineStage
        context: PipelineContext
    ) -> StageResult:
        """
        Execute a single pipeline stage.
        
        Args:
            stage: Stage to execute
            context: Pipeline context
            
        Returns:
            Stage execution result
        """
        stage_start = datetime.now()
        strategy_results: List[StrategyResult] = []
        
        # Build strategy instances
        strategies = []
        for strategy_config in stage.config.strategies:
            if not strategy_config.enabled:
                continue
            
            try:
                strategy = strategy_registry.create(
                    strategy_config.type,
                    strategy_config.config
                )
                strategies.append((strategy, strategy_config))
            except Exception as e:
                logger.error(f"Failed to create strategy {strategy_config.type}: {e}")
                strategy_results.append(
                    StrategyResult(
                        strategy_type=strategy_config.type,
                        documents=[],
                        error=str(e)
                    )
                )
        
        # Execute strategies based on mode
        if stage.is_parallel():
            # Execute strategies in parallel
            tasks = []
            for strategy, config in strategies:
                task = self._execute_strategy(strategy, context)
                tasks.append((task, config.weight))
            
            # Gather results
            results = await asyncio.gather(
                *[task for task, _ in tasks],
                return_exceptions=True
            )
            
            # Process results with weights
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    strategy_type = strategies[i][0].strategy_type
                    strategy_results.append(
                        StrategyResult(
                            strategy_type=strategy_type,
                            documents=[],
                            error=str(result)
                        )
                    )
                else:
                    strategy_results.append(result)
                    # Apply weight to scores
                    weight = tasks[i][1]
                    for doc in result.documents:
                        doc.similarity_score *= weight
        
        else:
            # Execute strategies in sequence
            for strategy, config in strategies:
                try:
                    result = await self._execute_strategy(strategy, context)
                    strategy_results.append(result)
                    # Update context for next strategy
                    context.update_documents(result.documents)
                except Exception as e:
                    strategy_results.append(
                        StrategyResult(
                            strategy_type=strategy.strategy_type,
                            documents=[],
                            error=str(e)
                        )
                    )
        
        # Merge results
        merged_documents = self._merge_stage_results(
            stage.config.mode,
            strategy_results
        )
        
        # Calculate execution time
        delta = datetime.now() - stage_start
        execution_time_ms = delta.total_seconds() * 1000
        
        return StageResult(
            stage_name=stage.config.name,
            mode=stage.config.mode,
            strategy_results=strategy_results,
            merged_documents=merged_documents,
            metadata={
                "strategy_count": len(strategies),
                "document_count": len(merged_documents)
            },
            execution_time_ms=execution_time_ms
        )
    
    async def _execute_strategy(
        self,
        strategy: Any,  # RAGStrategy
        context: PipelineContext
    ) -> StrategyResult:
        """
        Execute a single strategy.
        
        Args:
            strategy: Strategy to execute
            context: Pipeline context
            
        Returns:
            Strategy result
        """
        return await strategy.execute(context)
    
    def _merge_stage_results(
        self,
        mode: ExecutionMode,
        strategy_results: List[StrategyResult]
    ) -> List[SearchResult]:
        """
        Merge results from strategies in a stage.
        
        Args:
            mode: Execution mode
            strategy_results: Results from strategies
            
        Returns:
            Merged documents
        """
        # Extract successful results
        successful_results = [
            r for r in strategy_results 
            if r.error is None and r.documents
        ]
        
        if not successful_results:
            return []
        
        if mode == ExecutionMode.PARALLEL:
            # Merge parallel results (weighted combination)
            all_docs = []
            for result in successful_results:
                all_docs.extend(result.documents)
            
            return self.result_merger.merge_weighted(all_docs)
        
        else:
            # For sequence mode, use the last result
            return successful_results[-1].documents if successful_results else []
    
    def _create_error_result(
        self,
        pipeline_name: str,
        query: str,
        error: str,
        start_time: datetime,
        stage_results: Optional[List[StageResult]] = None
    ) -> PipelineResult:
        """
        Create an error pipeline result.
        
        Args:
            pipeline_name: Pipeline name
            query: Search query
            error: Error message
            start_time: Execution start time
            stage_results: Any stage results before error
            
        Returns:
            Error pipeline result
        """
        delta = datetime.now() - start_time
        execution_time_ms = delta.total_seconds() * 1000
        
        return PipelineResult(
            pipeline_name=pipeline_name,
            query=query,
            stage_results=stage_results or [],
            final_documents=[],
            metadata={"error": error},
            total_execution_time_ms=execution_time_ms,
            success=False,
            error=error
        )