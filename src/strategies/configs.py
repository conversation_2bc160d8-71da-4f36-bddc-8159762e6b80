"""Pre-built strategy configurations and templates."""

from typing import Any, Dict, List
from dataclasses import dataclass


@dataclass
class StrategyTemplate:
    """Template for creating strategy configurations."""
    
    name: str
    description: str
    config: Dict[str, Any]
    use_cases: List[str]


class StrategyConfigurations:
    """Collection of pre-built strategy configurations."""
    
    @staticmethod
    def multi_vector_search() -> StrategyTemplate:
        """
        Parallel search using multiple vector strategies.
        Good for: Comprehensive search, redundancy
        """
        return StrategyTemplate(
            name="multi_vector_search",
            description="Parallel search using semantic, keyword, and hybrid approaches",
            config={
                "type": "parallel_composer",
                "strategies": [
                    {
                        "type": "semantic_search",
                        "config": {"top_k": 20, "similarity_threshold": 0.7}
                    },
                    {
                        "type": "keyword_search", 
                        "config": {"top_k": 15, "boost_exact_match": True}
                    },
                    {
                        "type": "hybrid_search",
                        "config": {"top_k": 18, "semantic_weight": 0.7}
                    }
                ],
                "weights": [1.0, 0.8, 0.9],
                "merge_method": "weighted"
            },
            use_cases=[
                "Comprehensive document retrieval",
                "When you need high recall",
                "Complex queries with mixed intent"
            ]
        )
    
    @staticmethod
    def search_enhance_rerank() -> StrategyTemplate:
        """
        Sequential pipeline: search -> enhance -> rerank.
        Good for: High-quality results, specialized domains
        """
        return StrategyTemplate(
            name="search_enhance_rerank",
            description="Sequential search, enhancement, and reranking pipeline",
            config={
                "type": "hybrid_composer",
                "search_strategies": [
                    {
                        "type": "semantic_search",
                        "config": {"top_k": 50}
                    }
                ],
                "enhance_strategies": [
                    {
                        "type": "context_enhancer",
                        "config": {"context_window": 500}
                    },
                    {
                        "type": "metadata_enricher", 
                        "config": {"include_source": True}
                    }
                ],
                "rerank_strategies": [
                    {
                        "type": "cross_encoder_rerank",
                        "config": {"top_k": 10, "model": "ms-marco-MiniLM-L-12-v2"}
                    }
                ],
                "flow": "search_enhance_rerank"
            },
            use_cases=[
                "High-precision retrieval",
                "Domain-specific queries",
                "When quality over quantity matters"
            ]
        )
    
    @staticmethod
    def adaptive_search() -> StrategyTemplate:
        """
        Conditional strategy based on query characteristics.
        Good for: Mixed query types, dynamic systems
        """
        return StrategyTemplate(
            name="adaptive_search",
            description="Adapts strategy based on query type and context",
            config={
                "type": "conditional_composer",
                "strategies": [
                    {
                        "type": "keyword_search",
                        "config": {"top_k": 15, "exact_match_boost": 2.0},
                        "condition": {
                            "query_contains": ["specific", "exact", "definition", "what is"]
                        }
                    },
                    {
                        "type": "semantic_search",
                        "config": {"top_k": 20, "similarity_threshold": 0.6},
                        "condition": {
                            "query_contains": ["similar", "like", "related", "concept"]
                        }
                    },
                    {
                        "type": "multi_vector_search",
                        "config": {
                            "type": "parallel_composer",
                            "strategies": [
                                {"type": "semantic_search", "config": {"top_k": 15}},
                                {"type": "keyword_search", "config": {"top_k": 10}}
                            ],
                            "merge_method": "weighted"
                        },
                        "condition": {
                            "document_count": {"operator": "gte", "value": 100}
                        }
                    }
                ],
                "default_strategy": {
                    "type": "semantic_search",
                    "config": {"top_k": 15}
                }
            },
            use_cases=[
                "Mixed query workloads",
                "User-facing search systems",
                "When query intent varies widely"
            ]
        )
    
    @staticmethod
    def iterative_refinement() -> StrategyTemplate:
        """
        Sequential refinement: search -> filter -> expand -> rerank.
        Good for: Precision-focused tasks, iterative improvement
        """
        return StrategyTemplate(
            name="iterative_refinement",
            description="Iteratively refines results through multiple stages",
            config={
                "type": "sequential_composer",
                "strategies": [
                    {
                        "type": "semantic_search",
                        "config": {"top_k": 100, "similarity_threshold": 0.5}
                    },
                    {
                        "type": "relevance_filter",
                        "config": {"min_score": 0.7, "max_results": 50}
                    },
                    {
                        "type": "context_expander",
                        "config": {"expansion_ratio": 1.5, "related_docs": True}
                    },
                    {
                        "type": "diversity_rerank",
                        "config": {"top_k": 15, "diversity_weight": 0.3}
                    }
                ],
                "pass_mode": "replace"
            },
            use_cases=[
                "Research and analysis tasks",
                "When you need diverse, high-quality results",
                "Complex information needs"
            ]
        )
    
    @staticmethod
    def fast_approximate() -> StrategyTemplate:
        """
        Fast search with approximate results.
        Good for: Real-time systems, large datasets
        """
        return StrategyTemplate(
            name="fast_approximate",
            description="Optimized for speed with approximate results",
            config={
                "type": "parallel_composer",
                "strategies": [
                    {
                        "type": "approximate_search",
                        "config": {"top_k": 10, "quality_threshold": 0.6}
                    },
                    {
                        "type": "cached_search",
                        "config": {"cache_size": 1000, "ttl_seconds": 300}
                    }
                ],
                "weights": [0.7, 0.3],
                "merge_method": "union"
            },
            use_cases=[
                "Real-time applications",
                "Large-scale systems",
                "When speed is critical"
            ]
        )
    
    @staticmethod
    def domain_expert() -> StrategyTemplate:
        """
        Domain-specific search with specialized models.
        Good for: Scientific, legal, medical domains
        """
        return StrategyTemplate(
            name="domain_expert",
            description="Domain-specific search with specialized processing",
            config={
                "type": "hybrid_composer",
                "search_strategies": [
                    {
                        "type": "domain_semantic_search",
                        "config": {
                            "model": "domain-specific-model",
                            "top_k": 30
                        }
                    }
                ],
                "enhance_strategies": [
                    {
                        "type": "terminology_enhancer",
                        "config": {"domain_glossary": True}
                    },
                    {
                        "type": "citation_enricher",
                        "config": {"include_references": True}
                    }
                ],
                "rerank_strategies": [
                    {
                        "type": "domain_relevance_rerank",
                        "config": {"domain_weights": True, "top_k": 10}
                    }
                ],
                "flow": "search_enhance_rerank"
            },
            use_cases=[
                "Scientific research",
                "Legal document analysis", 
                "Medical information retrieval",
                "Technical documentation"
            ]
        )
    
    @staticmethod
    def ensemble_voting() -> StrategyTemplate:
        """
        Multiple strategies with voting-based result selection.
        Good for: High-confidence results, consensus building
        """
        return StrategyTemplate(
            name="ensemble_voting",
            description="Multiple strategies with consensus-based ranking",
            config={
                "type": "parallel_composer",
                "strategies": [
                    {
                        "type": "semantic_search",
                        "config": {"top_k": 25}
                    },
                    {
                        "type": "keyword_search",
                        "config": {"top_k": 25}
                    },
                    {
                        "type": "graph_search",
                        "config": {"top_k": 25, "walk_length": 3}
                    },
                    {
                        "type": "bm25_search",
                        "config": {"top_k": 25, "k1": 1.2, "b": 0.75}
                    }
                ],
                "weights": [1.0, 1.0, 1.0, 1.0],
                "merge_method": "voting_consensus"
            },
            use_cases=[
                "High-stakes decisions",
                "When you need consensus",
                "Quality assurance scenarios"
            ]
        )
    
    @staticmethod
    def get_all_templates() -> Dict[str, StrategyTemplate]:
        """Get all available strategy templates."""
        return {
            "multi_vector_search": StrategyConfigurations.multi_vector_search(),
            "search_enhance_rerank": StrategyConfigurations.search_enhance_rerank(),
            "adaptive_search": StrategyConfigurations.adaptive_search(),
            "iterative_refinement": StrategyConfigurations.iterative_refinement(),
            "fast_approximate": StrategyConfigurations.fast_approximate(),
            "domain_expert": StrategyConfigurations.domain_expert(),
            "ensemble_voting": StrategyConfigurations.ensemble_voting()
        }
    
    @staticmethod
    def get_template_by_use_case(use_case: str) -> List[StrategyTemplate]:
        """
        Get templates that match a specific use case.
        
        Args:
            use_case: Use case description
            
        Returns:
            List of matching templates
        """
        templates = StrategyConfigurations.get_all_templates()
        matching = []
        
        for template in templates.values():
            if any(use_case.lower() in uc.lower() for uc in template.use_cases):
                matching.append(template)
        
        return matching


class StrategyConfigBuilder:
    """Builder for creating custom strategy configurations."""
    
    def __init__(self):
        """Initialize builder."""
        self.config = {"strategies": []}
    
    def parallel(self) -> "StrategyConfigBuilder":
        """Set parallel execution mode."""
        self.config["type"] = "parallel_composer"
        return self
    
    def sequential(self, pass_mode: str = "replace") -> "StrategyConfigBuilder":
        """Set sequential execution mode."""
        self.config["type"] = "sequential_composer"
        self.config["pass_mode"] = pass_mode
        return self
    
    def conditional(self) -> "StrategyConfigBuilder":
        """Set conditional execution mode."""
        self.config["type"] = "conditional_composer"
        return self
    
    def hybrid(self, flow: str = "search_enhance_rerank") -> "StrategyConfigBuilder":
        """Set hybrid execution mode."""
        self.config["type"] = "hybrid_composer"
        self.config["flow"] = flow
        return self
    
    def add_strategy(
        self, 
        strategy_type: str, 
        config: Dict[str, Any],
        weight: float = 1.0,
        condition: Dict[str, Any] = None
    ) -> "StrategyConfigBuilder":
        """Add a strategy to the configuration."""
        strategy_def = {
            "type": strategy_type,
            "config": config
        }
        
        if condition:
            strategy_def["condition"] = condition
        
        self.config["strategies"].append(strategy_def)
        
        # Add weight if parallel
        if self.config.get("type") == "parallel_composer":
            if "weights" not in self.config:
                self.config["weights"] = []
            self.config["weights"].append(weight)
        
        return self
    
    def set_merge_method(self, method: str) -> "StrategyConfigBuilder":
        """Set merge method for parallel execution."""
        self.config["merge_method"] = method
        return self
    
    def set_default_strategy(
        self, 
        strategy_type: str, 
        config: Dict[str, Any]
    ) -> "StrategyConfigBuilder":
        """Set default strategy for conditional execution."""
        self.config["default_strategy"] = {
            "type": strategy_type,
            "config": config
        }
        return self
    
    def build(self) -> Dict[str, Any]:
        """Build the final configuration."""
        return self.config.copy()


# Example usage functions
def create_simple_parallel_search(top_k: int = 15) -> Dict[str, Any]:
    """Create a simple parallel search configuration."""
    return (
        StrategyConfigBuilder()
        .parallel()
        .add_strategy("semantic_search", {"top_k": top_k})
        .add_strategy("keyword_search", {"top_k": top_k}, weight=0.8)
        .set_merge_method("weighted")
        .build()
    )


def create_quality_focused_pipeline(domain: str = None) -> Dict[str, Any]:
    """Create a quality-focused search pipeline."""
    builder = (
        StrategyConfigBuilder()
        .sequential("replace")
        .add_strategy("semantic_search", {"top_k": 50, "similarity_threshold": 0.6})
        .add_strategy("relevance_filter", {"min_score": 0.7})
    )
    
    if domain:
        builder.add_strategy("domain_rerank", {"domain": domain, "top_k": 15})
    else:
        builder.add_strategy("cross_encoder_rerank", {"top_k": 15})
    
    return builder.build()


def create_adaptive_pipeline() -> Dict[str, Any]:
    """Create an adaptive pipeline that adjusts based on query."""
    return (
        StrategyConfigBuilder()
        .conditional()
        .add_strategy(
            "keyword_search", 
            {"top_k": 15, "exact_match_boost": 2.0},
            condition={"query_contains": ["exact", "specific", "definition"]}
        )
        .add_strategy(
            "semantic_search",
            {"top_k": 20},
            condition={"query_contains": ["similar", "related", "like"]}
        )
        .set_default_strategy("semantic_search", {"top_k": 15})
        .build()
    )