"""Tests for strategy configuration templates and builders."""

import pytest

from crawl4ai_mcp.strategies.configs import (
    StrategyConfigurations,
    StrategyTemplate,
    StrategyConfigBuilder,
    create_simple_parallel_search,
    create_quality_focused_pipeline,
    create_adaptive_pipeline
)


class TestStrategyTemplate:
    """Test StrategyTemplate dataclass."""
    
    def test_template_creation(self):
        """Test creating a strategy template."""
        template = StrategyTemplate(
            name="test_template",
            description="A test template",
            config={"type": "test", "param": "value"},
            use_cases=["testing", "examples"]
        )
        
        assert template.name == "test_template"
        assert template.description == "A test template"
        assert template.config["type"] == "test"
        assert "testing" in template.use_cases


class TestStrategyConfigurations:
    """Test pre-built strategy configurations."""
    
    def test_multi_vector_search_template(self):
        """Test multi-vector search configuration."""
        template = StrategyConfigurations.multi_vector_search()
        
        assert template.name == "multi_vector_search"
        assert template.config["type"] == "parallel_composer"
        assert len(template.config["strategies"]) == 3
        assert len(template.config["weights"]) == 3
        assert template.config["merge_method"] == "weighted"
        assert "Comprehensive document retrieval" in template.use_cases
        
        # Check strategy types
        strategy_types = [s["type"] for s in template.config["strategies"]]
        assert "semantic_search" in strategy_types
        assert "keyword_search" in strategy_types
        assert "hybrid_search" in strategy_types
    
    def test_search_enhance_rerank_template(self):
        """Test search-enhance-rerank configuration."""
        template = StrategyConfigurations.search_enhance_rerank()
        
        assert template.name == "search_enhance_rerank"
        assert template.config["type"] == "hybrid_composer"
        assert template.config["flow"] == "search_enhance_rerank"
        assert len(template.config["search_strategies"]) == 1
        assert len(template.config["enhance_strategies"]) == 2
        assert len(template.config["rerank_strategies"]) == 1
        assert "High-precision retrieval" in template.use_cases
    
    def test_adaptive_search_template(self):
        """Test adaptive search configuration."""
        template = StrategyConfigurations.adaptive_search()
        
        assert template.name == "adaptive_search"
        assert template.config["type"] == "conditional_composer"
        assert len(template.config["strategies"]) == 3
        assert template.config["default_strategy"] is not None
        assert "Mixed query workloads" in template.use_cases
        
        # Check conditions
        first_condition = template.config["strategies"][0]["condition"]
        assert "query_contains" in first_condition
        assert "specific" in first_condition["query_contains"]
    
    def test_iterative_refinement_template(self):
        """Test iterative refinement configuration."""
        template = StrategyConfigurations.iterative_refinement()
        
        assert template.name == "iterative_refinement"
        assert template.config["type"] == "sequential_composer"
        assert template.config["pass_mode"] == "replace"
        assert len(template.config["strategies"]) == 4
        assert "Research and analysis tasks" in template.use_cases
    
    def test_fast_approximate_template(self):
        """Test fast approximate configuration."""
        template = StrategyConfigurations.fast_approximate()
        
        assert template.name == "fast_approximate"
        assert template.config["type"] == "parallel_composer"
        assert template.config["merge_method"] == "union"
        assert "Real-time applications" in template.use_cases
    
    def test_domain_expert_template(self):
        """Test domain expert configuration."""
        template = StrategyConfigurations.domain_expert()
        
        assert template.name == "domain_expert"
        assert template.config["type"] == "hybrid_composer"
        assert "domain-specific-model" in str(template.config)
        assert "Scientific research" in template.use_cases
    
    def test_ensemble_voting_template(self):
        """Test ensemble voting configuration."""
        template = StrategyConfigurations.ensemble_voting()
        
        assert template.name == "ensemble_voting"
        assert template.config["type"] == "parallel_composer"
        assert template.config["merge_method"] == "voting_consensus"
        assert len(template.config["strategies"]) == 4
        assert all(w == 1.0 for w in template.config["weights"])
        assert "High-stakes decisions" in template.use_cases
    
    def test_get_all_templates(self):
        """Test getting all templates."""
        templates = StrategyConfigurations.get_all_templates()
        
        assert isinstance(templates, dict)
        assert len(templates) == 7  # Number of defined templates
        assert "multi_vector_search" in templates
        assert "adaptive_search" in templates
        
        # Check that all values are StrategyTemplate instances
        for template in templates.values():
            assert isinstance(template, StrategyTemplate)
    
    def test_get_template_by_use_case(self):
        """Test finding templates by use case."""
        # Test exact match
        real_time_templates = StrategyConfigurations.get_template_by_use_case("real-time")
        assert len(real_time_templates) > 0
        assert any("fast_approximate" in t.name for t in real_time_templates)
        
        # Test partial match
        research_templates = StrategyConfigurations.get_template_by_use_case("research")
        assert len(research_templates) > 0
        assert any("iterative_refinement" in t.name for t in research_templates)
        
        # Test case insensitive
        precision_templates = StrategyConfigurations.get_template_by_use_case("PRECISION")
        assert len(precision_templates) > 0
        
        # Test no match
        no_match_templates = StrategyConfigurations.get_template_by_use_case("nonexistent_use_case")
        assert len(no_match_templates) == 0


class TestStrategyConfigBuilder:
    """Test the strategy configuration builder."""
    
    def test_parallel_builder(self):
        """Test building parallel configuration."""
        config = (
            StrategyConfigBuilder()
            .parallel()
            .add_strategy("semantic_search", {"top_k": 10}, weight=1.2)
            .add_strategy("keyword_search", {"top_k": 5}, weight=0.8)
            .set_merge_method("weighted")
            .build()
        )
        
        assert config["type"] == "parallel_composer"
        assert len(config["strategies"]) == 2
        assert config["weights"] == [1.2, 0.8]
        assert config["merge_method"] == "weighted"
        
        # Check strategies
        assert config["strategies"][0]["type"] == "semantic_search"
        assert config["strategies"][0]["config"]["top_k"] == 10
        assert config["strategies"][1]["type"] == "keyword_search"
        assert config["strategies"][1]["config"]["top_k"] == 5
    
    def test_sequential_builder(self):
        """Test building sequential configuration."""
        config = (
            StrategyConfigBuilder()
            .sequential("merge")
            .add_strategy("search_strategy", {"param": "value1"})
            .add_strategy("enhance_strategy", {"param": "value2"})
            .build()
        )
        
        assert config["type"] == "sequential_composer"
        assert config["pass_mode"] == "merge"
        assert len(config["strategies"]) == 2
        assert "weights" not in config  # Sequential doesn't use weights
    
    def test_conditional_builder(self):
        """Test building conditional configuration."""
        condition = {"query_contains": ["specific"]}
        default_config = {"top_k": 10}
        
        config = (
            StrategyConfigBuilder()
            .conditional()
            .add_strategy("keyword_search", {"top_k": 15}, condition=condition)
            .add_strategy("semantic_search", {"top_k": 20})
            .set_default_strategy("fallback_search", default_config)
            .build()
        )
        
        assert config["type"] == "conditional_composer"
        assert len(config["strategies"]) == 2
        assert config["strategies"][0]["condition"] == condition
        assert "condition" not in config["strategies"][1]
        assert config["default_strategy"]["type"] == "fallback_search"
        assert config["default_strategy"]["config"] == default_config
    
    def test_hybrid_builder(self):
        """Test building hybrid configuration."""
        config = (
            StrategyConfigBuilder()
            .hybrid("search_enhance_rerank")
            .add_strategy("search_type", {"search_param": "value"})
            .build()
        )
        
        assert config["type"] == "hybrid_composer"
        assert config["flow"] == "search_enhance_rerank"
        assert len(config["strategies"]) == 1
    
    def test_builder_chaining(self):
        """Test that builder methods can be chained."""
        builder = StrategyConfigBuilder()
        
        # All methods should return the builder instance
        assert builder.parallel() is builder
        assert builder.add_strategy("test", {}) is builder
        assert builder.set_merge_method("union") is builder
    
    def test_empty_builder(self):
        """Test building with minimal configuration."""
        config = StrategyConfigBuilder().build()
        
        assert config["strategies"] == []
        assert "type" not in config  # Type not set


class TestConvenienceFunctions:
    """Test convenience functions for creating configurations."""
    
    def test_create_simple_parallel_search(self):
        """Test simple parallel search creation."""
        config = create_simple_parallel_search(top_k=20)
        
        assert config["type"] == "parallel_composer"
        assert config["merge_method"] == "weighted"
        assert len(config["strategies"]) == 2
        
        # Check that top_k is applied
        for strategy_def in config["strategies"]:
            assert strategy_def["config"]["top_k"] == 20
        
        # Check weights
        assert config["weights"] == [1.0, 0.8]
    
    def test_create_simple_parallel_search_default_top_k(self):
        """Test simple parallel search with default top_k."""
        config = create_simple_parallel_search()
        
        # Should use default top_k of 15
        for strategy_def in config["strategies"]:
            assert strategy_def["config"]["top_k"] == 15
    
    def test_create_quality_focused_pipeline(self):
        """Test quality-focused pipeline creation."""
        config = create_quality_focused_pipeline()
        
        assert config["type"] == "sequential_composer"
        assert config["pass_mode"] == "replace"
        assert len(config["strategies"]) == 3
        
        # Check strategy types
        strategy_types = [s["type"] for s in config["strategies"]]
        assert "semantic_search" in strategy_types
        assert "relevance_filter" in strategy_types
        assert "cross_encoder_rerank" in strategy_types
    
    def test_create_quality_focused_pipeline_with_domain(self):
        """Test quality-focused pipeline with domain specification."""
        config = create_quality_focused_pipeline(domain="medical")
        
        # Should use domain rerank instead of cross encoder
        strategy_types = [s["type"] for s in config["strategies"]]
        assert "domain_rerank" in strategy_types
        assert "cross_encoder_rerank" not in strategy_types
        
        # Check domain parameter
        domain_strategy = next(s for s in config["strategies"] if s["type"] == "domain_rerank")
        assert domain_strategy["config"]["domain"] == "medical"
    
    def test_create_adaptive_pipeline(self):
        """Test adaptive pipeline creation."""
        config = create_adaptive_pipeline()
        
        assert config["type"] == "conditional_composer"
        assert len(config["strategies"]) == 2
        assert config["default_strategy"] is not None
        
        # Check conditions
        first_strategy = config["strategies"][0]
        assert "condition" in first_strategy
        assert "query_contains" in first_strategy["condition"]
        
        second_strategy = config["strategies"][1]
        assert "condition" in second_strategy
        assert "query_contains" in second_strategy["condition"]


class TestConfigurationValidation:
    """Test configuration validation and error handling."""
    
    def test_valid_parallel_config(self):
        """Test that valid parallel configurations don't raise errors."""
        config = {
            "type": "parallel_composer",
            "strategies": [
                {"type": "test_strategy", "config": {}}
            ],
            "merge_method": "weighted"
        }
        
        # Should not raise an exception during validation
        # (This would be tested when the actual composer is created)
        assert config["type"] == "parallel_composer"
        assert len(config["strategies"]) == 1
    
    def test_configuration_immutability(self):
        """Test that build() returns a copy, not the original."""
        builder = StrategyConfigBuilder().parallel()
        config1 = builder.build()
        config2 = builder.build()
        
        # Modify one config
        config1["new_key"] = "new_value"
        
        # Other config should be unchanged
        assert "new_key" not in config2
    
    def test_template_config_validity(self):
        """Test that all template configurations are well-formed."""
        templates = StrategyConfigurations.get_all_templates()
        
        for name, template in templates.items():
            config = template.config
            
            # All configs should have a type
            assert "type" in config, f"Template {name} missing type"
            
            # Type should be a known composer type
            assert config["type"] in [
                "parallel_composer",
                "sequential_composer", 
                "conditional_composer",
                "hybrid_composer"
            ], f"Template {name} has unknown type: {config['type']}"
            
            # Should have strategies (except hybrid which might have typed strategies)
            if config["type"] != "hybrid_composer":
                assert "strategies" in config, f"Template {name} missing strategies"
                assert isinstance(config["strategies"], list), f"Template {name} strategies not a list"
            
            # Parallel should have reasonable merge method
            if config["type"] == "parallel_composer":
                if "merge_method" in config:
                    assert config["merge_method"] in [
                        "weighted", "union", "voting_consensus"
                    ], f"Template {name} has unknown merge method"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])