"""Tests for strategy combiners and composition utilities."""

import pytest
import asyncio
from typing import Dict, Any

from crawl4ai_mcp.models import SearchResult
from crawl4ai_mcp.pipelines.context import PipelineContext
from crawl4ai_mcp.pipelines.base import StrategyResult
from crawl4ai_mcp.strategies.base import RAGStrategy
from crawl4ai_mcp.strategies.registry import strategy_registry
from crawl4ai_mcp.strategies.combiners import (
    ParallelComposer,
    SequentialComposer,
    ConditionalComposer,
    HybridComposer
)


class MockStrategy(RAGStrategy):
    """Mock strategy for testing."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize mock strategy."""
        super().__init__(config)
        self.strategy_name = config.get("name", "mock")
        self.return_docs = config.get("return_docs", 5)
        self.delay = config.get("delay", 0.01)
        self.should_error = config.get("should_error", False)
        self.error_message = config.get("error_message", "Mock error")
    
    def _validate_config(self) -> None:
        """Validate configuration."""
        pass
    
    @property
    def strategy_type(self) -> str:
        """Return strategy type."""
        return f"mock_{self.strategy_name}"
    
    async def execute(self, context: PipelineContext) -> StrategyResult:
        """Execute mock strategy."""
        await asyncio.sleep(self.delay)
        
        if self.should_error:
            return self._create_result(
                documents=[],
                error=self.error_message
            )
        
        # Create mock documents
        docs = []
        for i in range(self.return_docs):
            docs.append(
                SearchResult(
                    id=f"{self.strategy_name}_doc_{i}",
                    content=f"Content from {self.strategy_name} strategy #{i}",
                    title=f"{self.strategy_name.title()} Document {i}",
                    url=f"https://example.com/{self.strategy_name}/{i}",
                    similarity_score=0.9 - (i * 0.1),
                    metadata={"source": self.strategy_name}
                )
            )
        
        return self._create_result(
            documents=docs,
            metadata={"strategy_name": self.strategy_name}
        )


@pytest.fixture
def sample_context():
    """Create sample pipeline context."""
    return PipelineContext(
        query="test query",
        initial_documents=[],
        metadata={"test": True}
    )


@pytest.fixture
def sample_documents():
    """Create sample search results."""
    return [
        SearchResult(
            id="doc_1",
            content="Sample document 1",
            title="Document 1",
            url="https://example.com/1",
            similarity_score=0.9,
            metadata={"source": "test"}
        ),
        SearchResult(
            id="doc_2", 
            content="Sample document 2",
            title="Document 2",
            url="https://example.com/2",
            similarity_score=0.8,
            metadata={"source": "test"}
        )
    ]


class TestParallelComposer:
    """Test parallel strategy composer."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Register mock strategies
        strategy_registry.register("mock_search", MockStrategy)
        strategy_registry.register("mock_enhance", MockStrategy)
    
    def teardown_method(self):
        """Clean up after tests."""
        strategy_registry.clear()
    
    @pytest.mark.asyncio
    async def test_parallel_execution(self, sample_context):
        """Test parallel execution of multiple strategies."""
        config = {
            "strategies": [
                {"type": "mock_search", "config": {"name": "search1", "return_docs": 3}},
                {"type": "mock_search", "config": {"name": "search2", "return_docs": 2}},
            ],
            "weights": [1.0, 0.8],
            "merge_method": "weighted"
        }
        
        composer = ParallelComposer(config)
        result = await composer.execute(sample_context)
        
        assert result.strategy_type == "parallel_composer"
        assert len(result.documents) == 5  # 3 + 2 documents
        assert result.error is None
        assert result.metadata["composer_type"] == "parallel"
        assert result.metadata["strategy_count"] == 2
        assert result.metadata["successful_count"] == 2
    
    @pytest.mark.asyncio
    async def test_parallel_with_error(self, sample_context):
        """Test parallel execution with one strategy failing."""
        config = {
            "strategies": [
                {"type": "mock_search", "config": {"name": "good", "return_docs": 2}},
                {"type": "mock_search", "config": {"name": "bad", "should_error": True}},
            ],
            "weights": [1.0, 1.0],
            "merge_method": "weighted"
        }
        
        composer = ParallelComposer(config)
        result = await composer.execute(sample_context)
        
        assert result.strategy_type == "parallel_composer" 
        assert len(result.documents) == 2  # Only from successful strategy
        assert result.error is None  # Composer itself didn't fail
        assert result.metadata["successful_count"] == 1
        assert len(result.metadata["errors"]) == 1
    
    @pytest.mark.asyncio
    async def test_weight_application(self, sample_context):
        """Test that weights are properly applied to document scores."""
        config = {
            "strategies": [
                {"type": "mock_search", "config": {"name": "high_weight", "return_docs": 1}},
                {"type": "mock_search", "config": {"name": "low_weight", "return_docs": 1}},
            ],
            "weights": [2.0, 0.5],
            "merge_method": "weighted"
        }
        
        composer = ParallelComposer(config)
        result = await composer.execute(sample_context)
        
        # Check that weights were applied
        high_weight_doc = next(d for d in result.documents if "high_weight" in d.id)
        low_weight_doc = next(d for d in result.documents if "low_weight" in d.id)
        
        # High weight doc should have higher score after weighting
        assert high_weight_doc.similarity_score > low_weight_doc.similarity_score
    
    def test_invalid_config(self):
        """Test composer with invalid configuration."""
        with pytest.raises(ValueError, match="strategies required"):
            ParallelComposer({})
        
        with pytest.raises(ValueError, match="non-empty list"):
            ParallelComposer({"strategies": []})


class TestSequentialComposer:
    """Test sequential strategy composer."""
    
    def setup_method(self):
        """Set up test fixtures."""
        strategy_registry.register("mock_search", MockStrategy)
        strategy_registry.register("mock_enhance", MockStrategy)
    
    def teardown_method(self):
        """Clean up after tests."""
        strategy_registry.clear()
    
    @pytest.mark.asyncio
    async def test_sequential_execution_replace(self, sample_context):
        """Test sequential execution with replace mode."""
        config = {
            "strategies": [
                {"type": "mock_search", "config": {"name": "first", "return_docs": 3}},
                {"type": "mock_enhance", "config": {"name": "second", "return_docs": 2}},
            ],
            "pass_mode": "replace"
        }
        
        composer = SequentialComposer(config)
        result = await composer.execute(sample_context)
        
        assert result.strategy_type == "sequential_composer"
        assert len(result.documents) == 2  # Only from last strategy
        assert result.error is None
        assert result.metadata["pass_mode"] == "replace"
        
        # All documents should be from the second strategy
        for doc in result.documents:
            assert "second" in doc.id
    
    @pytest.mark.asyncio
    async def test_sequential_execution_merge(self, sample_context):
        """Test sequential execution with merge mode."""
        config = {
            "strategies": [
                {"type": "mock_search", "config": {"name": "first", "return_docs": 2}},
                {"type": "mock_enhance", "config": {"name": "second", "return_docs": 2}},
            ],
            "pass_mode": "merge"
        }
        
        composer = SequentialComposer(config)
        result = await composer.execute(sample_context)
        
        assert result.strategy_type == "sequential_composer"
        assert len(result.documents) == 4  # Merged from both strategies
        assert result.error is None
    
    @pytest.mark.asyncio 
    async def test_sequential_with_error(self, sample_context):
        """Test sequential execution stops on error."""
        config = {
            "strategies": [
                {"type": "mock_search", "config": {"name": "good", "return_docs": 2}},
                {"type": "mock_search", "config": {"name": "bad", "should_error": True}},
                {"type": "mock_search", "config": {"name": "never_reached", "return_docs": 1}},
            ],
            "pass_mode": "replace"
        }
        
        composer = SequentialComposer(config)
        result = await composer.execute(sample_context)
        
        assert result.error is not None
        assert "Strategy 1" in result.error
        assert "Mock error" in result.error


class TestConditionalComposer:
    """Test conditional strategy composer."""
    
    def setup_method(self):
        """Set up test fixtures."""
        strategy_registry.register("mock_search", MockStrategy)
        strategy_registry.register("mock_keyword", MockStrategy)
    
    def teardown_method(self):
        """Clean up after tests."""
        strategy_registry.clear()
    
    @pytest.mark.asyncio
    async def test_condition_matching(self):
        """Test condition matching and strategy selection."""
        config = {
            "strategies": [
                {
                    "type": "mock_keyword",
                    "config": {"name": "keyword", "return_docs": 2},
                    "condition": {"query_contains": ["specific", "exact"]}
                },
                {
                    "type": "mock_search", 
                    "config": {"name": "semantic", "return_docs": 3},
                    "condition": {"query_contains": ["similar", "related"]}
                }
            ],
            "default_strategy": {
                "type": "mock_search",
                "config": {"name": "default", "return_docs": 1}
            }
        }
        
        composer = ConditionalComposer(config)
        
        # Test first condition match
        context1 = PipelineContext(query="find specific information", initial_documents=[])
        result1 = await composer.execute(context1)
        
        assert result1.metadata["selected_strategy"] == "mock_keyword"
        assert len(result1.documents) == 2
        
        # Test second condition match
        context2 = PipelineContext(query="find similar concepts", initial_documents=[])
        result2 = await composer.execute(context2)
        
        assert result2.metadata["selected_strategy"] == "mock_semantic"
        assert len(result2.documents) == 3
        
        # Test default strategy
        context3 = PipelineContext(query="random query", initial_documents=[])
        result3 = await composer.execute(context3)
        
        assert result3.metadata["selected_strategy"] == "mock_default"
        assert len(result3.documents) == 1
    
    @pytest.mark.asyncio
    async def test_document_count_condition(self, sample_documents):
        """Test document count conditions."""
        config = {
            "strategies": [
                {
                    "type": "mock_search",
                    "config": {"name": "many_docs", "return_docs": 5},
                    "condition": {"document_count": {"operator": "gte", "value": 2}}
                }
            ],
            "default_strategy": {
                "type": "mock_search",
                "config": {"name": "default", "return_docs": 1}
            }
        }
        
        composer = ConditionalComposer(config)
        
        # Test with enough documents
        context = PipelineContext(
            query="test",
            initial_documents=sample_documents,  # 2 documents
            metadata={}
        )
        result = await composer.execute(context)
        
        assert result.metadata["selected_strategy"] == "mock_many_docs"
    
    @pytest.mark.asyncio
    async def test_no_matching_condition(self, sample_context):
        """Test behavior when no conditions match and no default."""
        config = {
            "strategies": [
                {
                    "type": "mock_search",
                    "config": {"name": "never_match"},
                    "condition": {"query_contains": ["nonexistent_term"]}
                }
            ]
        }
        
        composer = ConditionalComposer(config)
        result = await composer.execute(sample_context)
        
        assert len(result.documents) == 0
        assert result.metadata["no_match"] is True


class TestHybridComposer:
    """Test hybrid strategy composer."""
    
    def setup_method(self):
        """Set up test fixtures."""
        strategy_registry.register("mock_search", MockStrategy)
        strategy_registry.register("mock_enhance", MockStrategy)
        strategy_registry.register("mock_rerank", MockStrategy)
    
    def teardown_method(self):
        """Clean up after tests."""
        strategy_registry.clear()
    
    @pytest.mark.asyncio 
    async def test_search_enhance_rerank_flow(self, sample_context):
        """Test the search -> enhance -> rerank flow."""
        config = {
            "search_strategies": [
                {"type": "mock_search", "config": {"name": "search1", "return_docs": 3}},
                {"type": "mock_search", "config": {"name": "search2", "return_docs": 2}},
            ],
            "enhance_strategies": [
                {"type": "mock_enhance", "config": {"name": "enhance1", "return_docs": 1}},
            ],
            "rerank_strategies": [
                {"type": "mock_rerank", "config": {"name": "rerank1", "return_docs": 1}},
            ],
            "flow": "search_enhance_rerank"
        }
        
        composer = HybridComposer(config)
        result = await composer.execute(sample_context)
        
        assert result.strategy_type == "hybrid_composer"
        assert result.metadata["flow"] == "search_enhance_rerank"
        assert result.metadata["search_count"] == 2
        assert result.metadata["enhance_count"] == 1
        assert result.metadata["rerank_count"] == 1
        assert len(result.documents) > 0
    
    @pytest.mark.asyncio
    async def test_parallel_search_sequential_flow(self, sample_context):
        """Test parallel search then sequential processing."""
        config = {
            "search_strategies": [
                {"type": "mock_search", "config": {"name": "search1", "return_docs": 2}},
                {"type": "mock_search", "config": {"name": "search2", "return_docs": 2}},
            ],
            "enhance_strategies": [
                {"type": "mock_enhance", "config": {"name": "enhance1", "return_docs": 1}},
            ],
            "rerank_strategies": [
                {"type": "mock_rerank", "config": {"name": "rerank1", "return_docs": 1}},
            ],
            "flow": "parallel_search_sequential_process"
        }
        
        composer = HybridComposer(config)
        result = await composer.execute(sample_context)
        
        assert result.strategy_type == "hybrid_composer"
        assert result.metadata["flow"] == "parallel_search_sequential_process"
        assert "initial_results" in result.metadata
        assert "final_results" in result.metadata
    
    @pytest.mark.asyncio
    async def test_unknown_flow(self, sample_context):
        """Test handling of unknown flow type."""
        config = {
            "search_strategies": [
                {"type": "mock_search", "config": {"name": "search1"}}
            ],
            "flow": "unknown_flow"
        }
        
        composer = HybridComposer(config)
        result = await composer.execute(sample_context)
        
        assert result.error is not None
        assert "Unknown flow" in result.error


class TestStrategyComposerIntegration:
    """Integration tests for strategy composers."""
    
    def setup_method(self):
        """Set up test fixtures."""
        strategy_registry.register("mock_search", MockStrategy)
        strategy_registry.register("parallel_composer", ParallelComposer)
        strategy_registry.register("sequential_composer", SequentialComposer)
    
    def teardown_method(self):
        """Clean up after tests."""
        strategy_registry.clear()
    
    @pytest.mark.asyncio
    async def test_nested_composition(self, sample_context):
        """Test composing composers (nested strategies)."""
        # Create a parallel composer as a strategy within a sequential composer
        nested_config = {
            "strategies": [
                {
                    "type": "parallel_composer",
                    "config": {
                        "strategies": [
                            {"type": "mock_search", "config": {"name": "inner1", "return_docs": 2}},
                            {"type": "mock_search", "config": {"name": "inner2", "return_docs": 2}},
                        ],
                        "merge_method": "weighted"
                    }
                },
                {
                    "type": "mock_search", 
                    "config": {"name": "outer", "return_docs": 1}
                }
            ],
            "pass_mode": "merge"
        }
        
        composer = SequentialComposer(nested_config)
        result = await composer.execute(sample_context)
        
        assert result.strategy_type == "sequential_composer"
        assert len(result.documents) == 5  # 4 from parallel + 1 from outer
        assert result.error is None
    
    @pytest.mark.asyncio
    async def test_performance_timing(self, sample_context):
        """Test that execution timing is properly tracked."""
        config = {
            "strategies": [
                {"type": "mock_search", "config": {"name": "slow", "delay": 0.1, "return_docs": 1}},
                {"type": "mock_search", "config": {"name": "fast", "delay": 0.01, "return_docs": 1}},
            ],
            "merge_method": "weighted"
        }
        
        composer = ParallelComposer(config)
        result = await composer.execute(sample_context)
        
        # Should have some execution time recorded
        assert result.execution_time_ms is not None
        assert result.execution_time_ms > 0
        
        # Parallel execution should be faster than sequential
        assert result.execution_time_ms < 200  # Should be less than sum of delays


@pytest.mark.asyncio
async def test_registry_integration():
    """Test that composers are properly registered and can be created."""
    # These should be registered automatically by the combiners module
    parallel_composer = strategy_registry.create("parallel_composer", {
        "strategies": [
            {"type": "mock_search", "config": {"name": "test"}}
        ]
    })
    
    assert isinstance(parallel_composer, ParallelComposer)
    assert parallel_composer.strategy_type == "parallel_composer"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])