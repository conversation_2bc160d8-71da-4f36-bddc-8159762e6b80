"""Strategy combination and composition utilities."""

from abc import abstractmethod
from typing import Any, Dict, List, Optional
from datetime import datetime
import asyncio
import logging

from crawl4ai_mcp.models import SearchResult
from crawl4ai_mcp.pipelines.context import PipelineContext
from crawl4ai_mcp.pipelines.base import StrategyResult
from crawl4ai_mcp.strategies.base import RAGStrategy
from crawl4ai_mcp.strategies.registry import strategy_registry
from crawl4ai_mcp.pipelines.mergers import ResultMerger

logger = logging.getLogger(__name__)


class StrategyComposer(RAGStrategy):
    """Base class for composing multiple strategies."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize strategy composer.
        
        Args:
            config: Configuration with strategy definitions
        """
        super().__init__(config)
        self.strategies = self._build_strategies()
        self.result_merger = ResultMerger()
    
    def _validate_config(self) -> None:
        """Validate composer configuration."""
        if "strategies" not in self.config:
            raise ValueError("strategies required in config")
        
        strategies = self.config["strategies"]
        if not isinstance(strategies, list) or not strategies:
            raise ValueError("strategies must be a non-empty list")
    
    def _build_strategies(self) -> List[RAGStrategy]:
        """Build strategy instances from config."""
        strategies = []
        
        for strategy_def in self.config["strategies"]:
            if isinstance(strategy_def, dict):
                strategy_type = strategy_def.get("type")
                strategy_config = strategy_def.get("config", {})
                
                if not strategy_type:
                    raise ValueError("Strategy type is required")
                
                strategy = strategy_registry.create(strategy_type, strategy_config)
                strategies.append(strategy)
            else:
                raise ValueError("Invalid strategy definition")
        
        return strategies
    
    @abstractmethod
    async def execute(self, context: PipelineContext) -> StrategyResult:
        """Execute the composed strategies."""
        pass


class ParallelComposer(StrategyComposer):
    """Execute multiple strategies in parallel and merge results."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize parallel composer.
        
        Args:
            config: Configuration with:
                - strategies: List of strategy definitions
                - weights: Optional weights for each strategy
                - merge_method: How to merge results (weighted, union, etc.)
        """
        super().__init__(config)
        self.weights = config.get("weights", [1.0] * len(self.strategies))
        self.merge_method = config.get("merge_method", "weighted")
    
    @property
    def strategy_type(self) -> str:
        """Return strategy type."""
        return "parallel_composer"
    
    async def execute(self, context: PipelineContext) -> StrategyResult:
        """Execute strategies in parallel and merge results."""
        start_time = datetime.now()
        
        try:
            # Execute all strategies in parallel
            tasks = [strategy.execute(context) for strategy in self.strategies]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            successful_results = []
            all_documents = []
            errors = []
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    errors.append(f"Strategy {i} failed: {str(result)}")
                    logger.error(f"Strategy {self.strategies[i].strategy_type} failed: {result}")
                elif result.error:
                    errors.append(f"Strategy {i} error: {result.error}")
                else:
                    successful_results.append(result)
                    # Apply weight to documents
                    weight = self.weights[i] if i < len(self.weights) else 1.0
                    for doc in result.documents:
                        weighted_doc = SearchResult(
                            **doc.dict(),
                            similarity_score=doc.similarity_score * weight
                        )
                        all_documents.append(weighted_doc)
            
            # Merge results based on method
            if self.merge_method == "weighted":
                merged_documents = self.result_merger.merge_weighted(all_documents)
            elif self.merge_method == "union":
                merged_documents = self.result_merger.merge_union(all_documents)
            else:
                merged_documents = all_documents
            
            return self._create_result(
                documents=merged_documents,
                metadata={
                    "composer_type": "parallel",
                    "strategy_count": len(self.strategies),
                    "successful_count": len(successful_results),
                    "merge_method": self.merge_method,
                    "errors": errors if errors else None
                },
                start_time=start_time
            )
            
        except Exception as e:
            logger.error(f"Parallel composer failed: {e}")
            return self._create_result(
                documents=[],
                error=str(e),
                start_time=start_time
            )


class SequentialComposer(StrategyComposer):
    """Execute strategies sequentially, passing results between them."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize sequential composer.
        
        Args:
            config: Configuration with:
                - strategies: List of strategy definitions
                - pass_mode: How to pass data (replace, merge, filter)
        """
        super().__init__(config)
        self.pass_mode = config.get("pass_mode", "replace")
    
    @property
    def strategy_type(self) -> str:
        """Return strategy type."""
        return "sequential_composer"
    
    async def execute(self, context: PipelineContext) -> StrategyResult:
        """Execute strategies sequentially."""
        start_time = datetime.now()
        current_documents = context.current_documents.copy()
        all_metadata = {}
        
        try:
            for i, strategy in enumerate(self.strategies):
                # Create context for this strategy
                strategy_context = PipelineContext(
                    query=context.query,
                    initial_documents=current_documents,
                    metadata=context.metadata
                )
                
                # Execute strategy
                result = await strategy.execute(strategy_context)
                
                if result.error:
                    return self._create_result(
                        documents=current_documents,
                        error=f"Strategy {i} ({strategy.strategy_type}) failed: {result.error}",
                        metadata=all_metadata,
                        start_time=start_time
                    )
                
                # Update documents based on pass mode
                if self.pass_mode == "replace":
                    current_documents = result.documents
                elif self.pass_mode == "merge":
                    current_documents = self.result_merger.merge_union(
                        current_documents + result.documents
                    )
                elif self.pass_mode == "filter":
                    # Keep only documents that appear in both
                    current_ids = {doc.id for doc in current_documents}
                    current_documents = [
                        doc for doc in result.documents 
                        if doc.id in current_ids
                    ]
                
                # Accumulate metadata
                all_metadata[f"stage_{i}_{strategy.strategy_type}"] = result.metadata
            
            return self._create_result(
                documents=current_documents,
                metadata={
                    "composer_type": "sequential",
                    "strategy_count": len(self.strategies),
                    "pass_mode": self.pass_mode,
                    "stage_metadata": all_metadata
                },
                start_time=start_time
            )
            
        except Exception as e:
            logger.error(f"Sequential composer failed: {e}")
            return self._create_result(
                documents=[],
                error=str(e),
                start_time=start_time
            )


class ConditionalComposer(StrategyComposer):
    """Execute strategies based on conditions."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize conditional composer.
        
        Args:
            config: Configuration with:
                - strategies: List of strategy definitions with conditions
                - default_strategy: Optional default if no conditions match
        """
        super().__init__(config)
        self.default_strategy = self._build_default_strategy()
    
    def _build_default_strategy(self) -> Optional[RAGStrategy]:
        """Build default strategy if configured."""
        default_config = self.config.get("default_strategy")
        if default_config:
            return strategy_registry.create(
                default_config["type"],
                default_config.get("config", {})
            )
        return None
    
    @property
    def strategy_type(self) -> str:
        """Return strategy type."""
        return "conditional_composer"
    
    async def execute(self, context: PipelineContext) -> StrategyResult:
        """Execute strategies based on conditions."""
        start_time = datetime.now()
        
        try:
            # Evaluate conditions and select strategy
            selected_strategy = None
            
            for i, strategy_def in enumerate(self.config["strategies"]):
                condition = strategy_def.get("condition", {})
                
                if self._evaluate_condition(condition, context):
                    selected_strategy = self.strategies[i]
                    break
            
            # Use default if no conditions matched
            if not selected_strategy:
                selected_strategy = self.default_strategy
            
            if not selected_strategy:
                return self._create_result(
                    documents=[],
                    metadata={"composer_type": "conditional", "no_match": True},
                    start_time=start_time
                )
            
            # Execute selected strategy
            result = await selected_strategy.execute(context)
            
            return self._create_result(
                documents=result.documents,
                metadata={
                    "composer_type": "conditional",
                    "selected_strategy": selected_strategy.strategy_type,
                    "inner_metadata": result.metadata
                },
                error=result.error,
                start_time=start_time
            )
            
        except Exception as e:
            logger.error(f"Conditional composer failed: {e}")
            return self._create_result(
                documents=[],
                error=str(e),
                start_time=start_time
            )
    
    def _evaluate_condition(
        self, 
        condition: Dict[str, Any], 
        context: PipelineContext
    ) -> bool:
        """
        Evaluate a condition against the context.
        
        Args:
            condition: Condition specification
            context: Pipeline context
            
        Returns:
            Whether condition is met
        """
        # Simple condition evaluation
        # Can be extended with more complex logic
        
        if "query_contains" in condition:
            terms = condition["query_contains"]
            if isinstance(terms, str):
                terms = [terms]
            return any(term.lower() in context.query.lower() for term in terms)
        
        if "document_count" in condition:
            op = condition["document_count"].get("operator", "gte")
            value = condition["document_count"].get("value", 0)
            count = len(context.current_documents)
            
            if op == "gte":
                return count >= value
            elif op == "lte":
                return count <= value
            elif op == "eq":
                return count == value
        
        if "has_metadata" in condition:
            key = condition["has_metadata"]
            return key in context.metadata
        
        # No conditions specified means always true
        return True


class HybridComposer(StrategyComposer):
    """Combine different types of strategies with custom logic."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize hybrid composer.
        
        Args:
            config: Configuration with:
                - search_strategies: Search strategies to use
                - enhance_strategies: Enhancement strategies
                - rerank_strategies: Reranking strategies
                - flow: Execution flow (search_first, enhance_all, etc.)
        """
        super().__init__(config)
        self.search_strategies = self._build_typed_strategies("search_strategies")
        self.enhance_strategies = self._build_typed_strategies("enhance_strategies")
        self.rerank_strategies = self._build_typed_strategies("rerank_strategies")
        self.flow = config.get("flow", "search_enhance_rerank")
    
    def _build_typed_strategies(self, key: str) -> List[RAGStrategy]:
        """Build strategies of a specific type."""
        strategies = []
        for strategy_def in self.config.get(key, []):
            strategy = strategy_registry.create(
                strategy_def["type"],
                strategy_def.get("config", {})
            )
            strategies.append(strategy)
        return strategies
    
    @property
    def strategy_type(self) -> str:
        """Return strategy type."""
        return "hybrid_composer"
    
    async def execute(self, context: PipelineContext) -> StrategyResult:
        """Execute hybrid strategy flow."""
        start_time = datetime.now()
        
        try:
            if self.flow == "search_enhance_rerank":
                return await self._search_enhance_rerank_flow(context, start_time)
            elif self.flow == "parallel_search_sequential_process":
                return await self._parallel_search_sequential_flow(context, start_time)
            else:
                return self._create_result(
                    documents=[],
                    error=f"Unknown flow: {self.flow}",
                    start_time=start_time
                )
                
        except Exception as e:
            logger.error(f"Hybrid composer failed: {e}")
            return self._create_result(
                documents=[],
                error=str(e),
                start_time=start_time
            )
    
    async def _search_enhance_rerank_flow(
        self, 
        context: PipelineContext,
        start_time: datetime
    ) -> StrategyResult:
        """Execute search -> enhance -> rerank flow."""
        documents = context.current_documents.copy()
        
        # Step 1: Search (parallel)
        if self.search_strategies:
            search_results = await asyncio.gather(
                *[s.execute(context) for s in self.search_strategies]
            )
            
            # Merge search results
            all_search_docs = []
            for result in search_results:
                if not result.error:
                    all_search_docs.extend(result.documents)
            
            documents = self.result_merger.merge_weighted(all_search_docs)
        
        # Step 2: Enhance (sequential on each document)
        if self.enhance_strategies and documents:
            enhanced_docs = []
            for doc in documents:
                doc_context = PipelineContext(
                    query=context.query,
                    initial_documents=[doc],
                    metadata=context.metadata
                )
                
                # Apply all enhancements
                current_doc = doc
                for enhance_strategy in self.enhance_strategies:
                    result = await enhance_strategy.execute(doc_context)
                    if not result.error and result.documents:
                        current_doc = result.documents[0]
                        doc_context.update_documents([current_doc])
                
                enhanced_docs.append(current_doc)
            
            documents = enhanced_docs
        
        # Step 3: Rerank (sequential)
        if self.rerank_strategies and documents:
            rerank_context = PipelineContext(
                query=context.query,
                initial_documents=documents,
                metadata=context.metadata
            )
            
            for rerank_strategy in self.rerank_strategies:
                result = await rerank_strategy.execute(rerank_context)
                if not result.error:
                    documents = result.documents
                    rerank_context.update_documents(documents)
        
        return self._create_result(
            documents=documents,
            metadata={
                "composer_type": "hybrid",
                "flow": self.flow,
                "search_count": len(self.search_strategies),
                "enhance_count": len(self.enhance_strategies),
                "rerank_count": len(self.rerank_strategies)
            },
            start_time=start_time
        )
    
    async def _parallel_search_sequential_flow(
        self, 
        context: PipelineContext,
        start_time: datetime
    ) -> StrategyResult:
        """Execute parallel search then sequential processing."""
        # Parallel search
        if not self.search_strategies:
            return self._create_result(
                documents=[],
                error="No search strategies configured",
                start_time=start_time
            )
        
        search_results = await asyncio.gather(
            *[s.execute(context) for s in self.search_strategies]
        )
        
        # Merge search results
        all_docs = []
        for result in search_results:
            if not result.error:
                all_docs.extend(result.documents)
        
        documents = self.result_merger.merge_weighted(all_docs)
        
        # Sequential enhancement and reranking
        process_context = PipelineContext(
            query=context.query,
            initial_documents=documents,
            metadata=context.metadata
        )
        
        # Apply all strategies sequentially
        all_strategies = self.enhance_strategies + self.rerank_strategies
        for strategy in all_strategies:
            result = await strategy.execute(process_context)
            if not result.error:
                documents = result.documents
                process_context.update_documents(documents)
        
        return self._create_result(
            documents=documents,
            metadata={
                "composer_type": "hybrid",
                "flow": self.flow,
                "initial_results": len(all_docs),
                "final_results": len(documents)
            },
            start_time=start_time
        )


# Register composers
strategy_registry.register("parallel_composer", ParallelComposer)
strategy_registry.register("sequential_composer", SequentialComposer)
strategy_registry.register("conditional_composer", ConditionalComposer)
strategy_registry.register("hybrid_composer", HybridComposer)