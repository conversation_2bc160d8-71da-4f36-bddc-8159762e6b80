"""Base classes for RAG strategies."""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from datetime import datetime

from crawl4ai_mcp.models import SearchResult
from crawl4ai_mcp.pipelines.context import PipelineContext
from crawl4ai_mcp.pipelines.base import StrategyResult


class RAGStrategy(ABC):
    """Abstract base class for all RAG strategies."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize strategy with configuration.
        
        Args:
            config: Strategy-specific configuration
        """
        self.config = config
        self._validate_config()
    
    @abstractmethod
    def _validate_config(self) -> None:
        """Validate strategy configuration."""
        pass
    
    @abstractmethod
    async def execute(
        self,
        context: PipelineContext
    ) -> StrategyResult:
        """
        Execute the strategy.
        
        Args:
            context: Pipeline execution context
            
        Returns:
            Strategy execution result
        """
        pass
    
    @property
    @abstractmethod
    def strategy_type(self) -> str:
        """Return the strategy type identifier."""
        pass
    
    def _create_result(
        self,
        documents: List[SearchResult],
        metadata: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None,
        start_time: Optional[datetime] = None
    ) -> StrategyResult:
        """
        Helper to create a strategy result.
        
        Args:
            documents: Result documents
            metadata: Additional metadata
            error: Error message if any
            start_time: Start time for execution time calculation
            
        Returns:
            StrategyResult instance
        """
        execution_time_ms = None
        if start_time:
            delta = datetime.now() - start_time
            execution_time_ms = delta.total_seconds() * 1000
        
        return StrategyResult(
            strategy_type=self.strategy_type,
            documents=documents,
            metadata=metadata or {},
            error=error,
            execution_time_ms=execution_time_ms
        )


class SearchStrategy(RAGStrategy):
    """Base class for search strategies."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize search strategy.
        
        Args:
            config: Strategy configuration
        """
        super().__init__(config)
        self.top_k = config.get("top_k", 10)
        self.min_score = config.get("min_score", 0.0)
    
    def _filter_by_score(
        self, 
        documents: List[SearchResult]
    ) -> List[SearchResult]:
        """
        Filter documents by minimum score.
        
        Args:
            documents: Documents to filter
            
        Returns:
            Filtered documents
        """
        if self.min_score <= 0:
            return documents
        
        return [
            doc for doc in documents 
            if doc.similarity_score >= self.min_score
        ]
    
    def _limit_results(
        self, 
        documents: List[SearchResult]
    ) -> List[SearchResult]:
        """
        Limit results to top_k.
        
        Args:
            documents: Documents to limit
            
        Returns:
            Limited documents
        """
        return documents[:self.top_k]


class EnhancementStrategy(RAGStrategy):
    """Base class for enhancement strategies."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize enhancement strategy.
        
        Args:
            config: Strategy configuration
        """
        super().__init__(config)
    
    def _should_enhance(self, document: SearchResult) -> bool:
        """
        Determine if a document should be enhanced.
        
        Args:
            document: Document to check
            
        Returns:
            Whether to enhance
        """
        # Can be overridden by subclasses
        return True


class RerankingStrategy(RAGStrategy):
    """Base class for reranking strategies."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize reranking strategy.
        
        Args:
            config: Strategy configuration
        """
        super().__init__(config)
        self.top_k = config.get("top_k", 10)
    
    def _apply_top_k(
        self, 
        documents: List[SearchResult]
    ) -> List[SearchResult]:
        """
        Apply top_k limit after reranking.
        
        Args:
            documents: Reranked documents
            
        Returns:
            Limited documents
        """
        return documents[:self.top_k]