"""Registry for RAG strategies."""

import logging
from typing import Any, Dict, Optional, Type

from crawl4ai_mcp.strategies.base import RAGStrategy

logger = logging.getLogger(__name__)


class StrategyRegistry:
    """Registry for managing RAG strategies."""
    
    _instance: Optional["StrategyRegistry"] = None
    
    def __new__(cls) -> "StrategyRegistry":
        """Ensure singleton instance."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._strategies = {}
        return cls._instance
    
    def register(
        self, 
        strategy_type: str, 
        strategy_class: Type[RAGStrategy]
    ) -> None:
        """
        Register a strategy.
        
        Args:
            strategy_type: Unique identifier for the strategy
            strategy_class: Strategy class
        """
        if strategy_type in self._strategies:
            logger.warning(f"Overwriting existing strategy: {strategy_type}")
        
        self._strategies[strategy_type] = strategy_class
        logger.info(f"Registered strategy: {strategy_type}")
    
    def get(self, strategy_type: str) -> Optional[Type[RAGStrategy]]:
        """
        Get a strategy class by type.
        
        Args:
            strategy_type: Strategy type identifier
            
        Returns:
            Strategy class or None
        """
        return self._strategies.get(strategy_type)
    
    def create(
        self, 
        strategy_type: str, 
        config: Dict[str, Any]
    ) -> RAGStrategy:
        """
        Create a strategy instance.
        
        Args:
            strategy_type: Strategy type identifier
            config: Strategy configuration
            
        Returns:
            Strategy instance
            
        Raises:
            ValueError: If strategy type not found
        """
        strategy_class = self.get(strategy_type)
        if not strategy_class:
            raise ValueError(f"Unknown strategy type: {strategy_type}")
        
        return strategy_class(config)
    
    def list_strategies(self) -> Dict[str, Type[RAGStrategy]]:
        """
        List all registered strategies.
        
        Returns:
            Dictionary of strategy types to classes
        """
        return self._strategies.copy()
    
    def clear(self) -> None:
        """Clear all registered strategies (mainly for testing)."""
        self._strategies.clear()


# Global registry instance
strategy_registry = StrategyRegistry()


def register_strategy(strategy_type: str):
    """
    Decorator to register a strategy class.
    
    Args:
        strategy_type: Strategy type identifier
        
    Returns:
        Decorator function
    """
    def decorator(cls: Type[RAGStrategy]) -> Type[RAGStrategy]:
        strategy_registry.register(strategy_type, cls)
        return cls
    return decorator