"""Example strategy implementations and usage patterns."""

import asyncio
from typing import Any, Dict
from datetime import datetime

from crawl4ai_mcp.models import SearchResult
from crawl4ai_mcp.pipelines.context import PipelineContext
from crawl4ai_mcp.pipelines.base import StrategyResult
from crawl4ai_mcp.strategies.base import RAGStrategy, SearchStrategy, EnhancementStrategy
from crawl4ai_mcp.strategies.registry import register_strategy, strategy_registry
from crawl4ai_mcp.strategies.configs import StrategyConfigurations, StrategyConfigBuilder


# Example basic strategies (normally these would be in separate files)

@register_strategy("semantic_search")
class MockSemanticSearch(SearchStrategy):
    """Mock semantic search for demonstration."""
    
    @property
    def strategy_type(self) -> str:
        return "semantic_search"
    
    async def execute(self, context: PipelineContext) -> StrategyResult:
        """Mock semantic search execution."""
        start_time = datetime.now()
        
        # Simulate semantic search
        await asyncio.sleep(0.1)  # Simulate processing time
        
        # Create mock results based on query
        mock_results = []
        for i in range(min(self.top_k, 10)):
            mock_results.append(
                SearchResult(
                    id=f"semantic_doc_{i}",
                    content=f"Semantic result {i} for query: {context.query}",
                    title=f"Semantic Document {i}",
                    url=f"https://example.com/semantic/{i}",
                    similarity_score=0.9 - (i * 0.05),
                    metadata={
                        "source": "semantic_search",
                        "embedding_model": "text-embedding-3-small"
                    }
                )
            )
        
        filtered_results = self._filter_by_score(mock_results)
        limited_results = self._limit_results(filtered_results)
        
        return self._create_result(
            documents=limited_results,
            metadata={
                "total_candidates": len(mock_results),
                "after_filtering": len(filtered_results),
                "strategy_config": self.config
            },
            start_time=start_time
        )


@register_strategy("keyword_search")
class MockKeywordSearch(SearchStrategy):
    """Mock keyword search for demonstration."""
    
    @property
    def strategy_type(self) -> str:
        return "keyword_search"
    
    async def execute(self, context: PipelineContext) -> StrategyResult:
        """Mock keyword search execution."""
        start_time = datetime.now()
        
        await asyncio.sleep(0.05)  # Simulate processing time
        
        # Create mock results
        mock_results = []
        query_words = context.query.lower().split()
        
        for i in range(min(self.top_k, 8)):
            # Simulate keyword matching scores
            score = 0.8 if any(word in f"keyword document {i}" for word in query_words) else 0.6
            
            mock_results.append(
                SearchResult(
                    id=f"keyword_doc_{i}",
                    content=f"Keyword result {i} matching: {context.query}",
                    title=f"Keyword Document {i}",
                    url=f"https://example.com/keyword/{i}",
                    similarity_score=score - (i * 0.03),
                    metadata={
                        "source": "keyword_search",
                        "matched_terms": query_words[:2]
                    }
                )
            )
        
        filtered_results = self._filter_by_score(mock_results)
        limited_results = self._limit_results(filtered_results)
        
        return self._create_result(
            documents=limited_results,
            metadata={
                "matched_terms": query_words,
                "total_results": len(mock_results)
            },
            start_time=start_time
        )


@register_strategy("context_enhancer")
class MockContextEnhancer(EnhancementStrategy):
    """Mock context enhancement for demonstration."""
    
    @property
    def strategy_type(self) -> str:
        return "context_enhancer"
    
    async def execute(self, context: PipelineContext) -> StrategyResult:
        """Mock context enhancement execution."""
        start_time = datetime.now()
        
        await asyncio.sleep(0.03)  # Simulate processing time
        
        enhanced_docs = []
        for doc in context.current_documents:
            if self._should_enhance(doc):
                # Simulate adding context
                enhanced_content = f"{doc.content}\n\nEnhanced Context: Additional relevant information about {context.query}"
                
                enhanced_doc = SearchResult(
                    **doc.dict(),
                    content=enhanced_content,
                    metadata={
                        **doc.metadata,
                        "enhanced": True,
                        "context_added": True,
                        "enhancement_query": context.query
                    }
                )
                enhanced_docs.append(enhanced_doc)
            else:
                enhanced_docs.append(doc)
        
        return self._create_result(
            documents=enhanced_docs,
            metadata={
                "enhanced_count": sum(1 for doc in enhanced_docs if doc.metadata.get("enhanced")),
                "total_processed": len(context.current_documents)
            },
            start_time=start_time
        )


@register_strategy("cross_encoder_rerank")
class MockCrossEncoderRerank(RAGStrategy):
    """Mock cross-encoder reranking for demonstration."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.top_k = config.get("top_k", 10)
        self.model = config.get("model", "default-cross-encoder")
    
    def _validate_config(self) -> None:
        """Validate configuration."""
        pass
    
    @property
    def strategy_type(self) -> str:
        return "cross_encoder_rerank"
    
    async def execute(self, context: PipelineContext) -> StrategyResult:
        """Mock cross-encoder reranking execution."""
        start_time = datetime.now()
        
        await asyncio.sleep(0.08)  # Simulate model inference time
        
        if not context.current_documents:
            return self._create_result(documents=[], start_time=start_time)
        
        # Simulate reranking by adjusting scores
        reranked_docs = []
        for doc in context.current_documents:
            # Simulate cross-encoder scoring
            query_doc_relevance = len(set(context.query.lower().split()) & 
                                   set(doc.content.lower().split())) / max(len(context.query.split()), 1)
            
            new_score = (doc.similarity_score * 0.7) + (query_doc_relevance * 0.3)
            
            reranked_doc = SearchResult(
                **doc.dict(),
                similarity_score=new_score,
                metadata={
                    **doc.metadata,
                    "reranked": True,
                    "rerank_model": self.model,
                    "original_score": doc.similarity_score
                }
            )
            reranked_docs.append(reranked_doc)
        
        # Sort by new scores and limit
        reranked_docs.sort(key=lambda x: x.similarity_score, reverse=True)
        final_docs = reranked_docs[:self.top_k]
        
        return self._create_result(
            documents=final_docs,
            metadata={
                "rerank_model": self.model,
                "input_count": len(context.current_documents),
                "output_count": len(final_docs),
                "score_improvement": sum(d.similarity_score for d in final_docs) / len(final_docs) if final_docs else 0
            },
            start_time=start_time
        )


# Example usage patterns

async def demonstrate_parallel_search():
    """Demonstrate parallel search strategy composition."""
    print("\\n=== Parallel Search Demonstration ===")
    
    # Create parallel search configuration
    config = StrategyConfigurations.multi_vector_search().config
    
    # Create the parallel composer
    parallel_strategy = strategy_registry.create("parallel_composer", config)
    
    # Create context
    context = PipelineContext(
        query="machine learning algorithms",
        initial_documents=[],
        metadata={"user_id": "demo_user"}
    )
    
    # Execute
    result = await parallel_strategy.execute(context)
    
    print(f"Strategy: {result.strategy_type}")
    print(f"Documents found: {len(result.documents)}")
    print(f"Execution time: {result.execution_time_ms:.2f}ms")
    print(f"Metadata: {result.metadata}")
    
    # Show top results
    for i, doc in enumerate(result.documents[:3]):
        print(f"  {i+1}. {doc.title} (score: {doc.similarity_score:.3f})")


async def demonstrate_sequential_refinement():
    """Demonstrate sequential strategy refinement."""
    print("\\n=== Sequential Refinement Demonstration ===")
    
    # Create sequential configuration
    config = StrategyConfigurations.iterative_refinement().config
    
    # Create the sequential composer  
    sequential_strategy = strategy_registry.create("sequential_composer", config)
    
    # Create context with some initial documents
    initial_docs = [
        SearchResult(
            id="init_1",
            content="Initial document about machine learning",
            title="ML Basics",
            url="https://example.com/ml-basics",
            similarity_score=0.8,
            metadata={"source": "initial"}
        )
    ]
    
    context = PipelineContext(
        query="deep learning neural networks",
        initial_documents=initial_docs,
        metadata={"refinement_stage": "initial"}
    )
    
    # Execute
    result = await sequential_strategy.execute(context)
    
    print(f"Strategy: {result.strategy_type}")
    print(f"Documents found: {len(result.documents)}")
    print(f"Stage metadata: {result.metadata.get('stage_metadata', {})}")
    
    # Show refined results
    for i, doc in enumerate(result.documents[:3]):
        print(f"  {i+1}. {doc.title} (score: {doc.similarity_score:.3f})")
        if doc.metadata.get("enhanced"):
            print(f"      Enhanced: {doc.metadata.get('enhanced')}")


async def demonstrate_adaptive_strategy():
    """Demonstrate adaptive/conditional strategy selection."""
    print("\\n=== Adaptive Strategy Demonstration ===")
    
    # Create adaptive configuration
    config = StrategyConfigurations.adaptive_search().config
    
    # Create the conditional composer
    adaptive_strategy = strategy_registry.create("conditional_composer", config)
    
    # Test different query types
    test_queries = [
        "What is machine learning?",  # Should trigger keyword search
        "Find similar concepts to neural networks",  # Should trigger semantic search
        "Complex query about distributed systems"  # Should use default
    ]
    
    for query in test_queries:
        print(f"\\nQuery: '{query}'")
        
        context = PipelineContext(
            query=query,
            initial_documents=[],
            metadata={"test_run": True}
        )
        
        result = await adaptive_strategy.execute(context)
        
        selected_strategy = result.metadata.get("selected_strategy", "unknown")
        print(f"  Selected strategy: {selected_strategy}")
        print(f"  Documents found: {len(result.documents)}")


async def demonstrate_hybrid_composition():
    """Demonstrate hybrid strategy composition."""
    print("\\n=== Hybrid Composition Demonstration ===")
    
    # Create hybrid configuration
    config = StrategyConfigurations.search_enhance_rerank().config
    
    # Create the hybrid composer
    hybrid_strategy = strategy_registry.create("hybrid_composer", config)
    
    # Create context
    context = PipelineContext(
        query="advanced machine learning techniques",
        initial_documents=[],
        metadata={"domain": "AI/ML"}
    )
    
    # Execute
    result = await hybrid_strategy.execute(context)
    
    print(f"Strategy: {result.strategy_type}")
    print(f"Flow: {result.metadata.get('flow')}")
    print(f"Search strategies: {result.metadata.get('search_count')}")
    print(f"Enhancement strategies: {result.metadata.get('enhance_count')}")  
    print(f"Rerank strategies: {result.metadata.get('rerank_count')}")
    print(f"Final documents: {len(result.documents)}")
    
    # Show enhanced results
    for i, doc in enumerate(result.documents[:3]):
        print(f"  {i+1}. {doc.title} (score: {doc.similarity_score:.3f})")
        if doc.metadata.get("enhanced"):
            print("      Enhanced: ✓")
        if doc.metadata.get("reranked"):
            print(f"      Reranked: ✓ (was {doc.metadata.get('original_score', 0):.3f})")


async def demonstrate_custom_builder():
    """Demonstrate building custom strategy configurations."""
    print("\\n=== Custom Builder Demonstration ===")
    
    # Build a custom parallel strategy
    custom_config = (
        StrategyConfigBuilder()
        .parallel()
        .add_strategy("semantic_search", {"top_k": 20, "similarity_threshold": 0.7}, weight=1.2)
        .add_strategy("keyword_search", {"top_k": 15, "exact_match_boost": True}, weight=0.9)  
        .add_strategy("context_enhancer", {"context_window": 300}, weight=0.5)
        .set_merge_method("weighted")
        .build()
    )
    
    print("Custom configuration:")
    print(f"  Type: {custom_config['type']}")
    print(f"  Strategies: {len(custom_config['strategies'])}")
    print(f"  Weights: {custom_config['weights']}")
    print(f"  Merge method: {custom_config['merge_method']}")
    
    # Create and execute
    custom_strategy = strategy_registry.create("parallel_composer", custom_config)
    
    context = PipelineContext(
        query="custom search example",
        initial_documents=[],
        metadata={"custom_build": True}
    )
    
    result = await custom_strategy.execute(context)
    
    print("\\nResults:")
    print(f"  Documents: {len(result.documents)}")
    print(f"  Execution time: {result.execution_time_ms:.2f}ms")


async def run_all_demonstrations():
    """Run all strategy demonstrations."""
    print("Starting RAG Strategy Combination Demonstrations")
    print("=" * 50)
    
    try:
        await demonstrate_parallel_search()
        await demonstrate_sequential_refinement()
        await demonstrate_adaptive_strategy()
        await demonstrate_hybrid_composition()
        await demonstrate_custom_builder()
        
        print("\\n" + "=" * 50)
        print("All demonstrations completed successfully!")
        
    except Exception as e:
        print(f"\\nError during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Run demonstrations
    asyncio.run(run_all_demonstrations())